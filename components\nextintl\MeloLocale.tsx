"use client";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useParams, usePathname, useRouter } from "next/navigation";

import { MdLanguage } from "react-icons/md";
import { supportedLanguages } from "@/config/languages";
export default function MeloLocale() {

  const params = useParams();
  const locale = params.locale as string;
  const router = useRouter();
  const pathname = usePathname();

  const handleSwitchLanguage = (value: string) => {
    if (value !== locale) {
      let newPathName = pathname.replace(`/${locale}`, `/${value}`);
      if (!newPathName.startsWith(`/${value}`)) {
        newPathName = `/${value}${newPathName}`;
      }
      router.push(newPathName);
    }
  };

  return (
    <Select value={locale} onValueChange={handleSwitchLanguage} >
      <SelectTrigger className="min-w-[135px] h-[40px] bg-background text-foreground border-border">
        <MdLanguage className="text-xl text-foreground" />
        <SelectValue className="hidden md:block" placeholder="Language" />
      </SelectTrigger>
      <SelectContent className="bg-background text-foreground">
        {supportedLanguages.map((lang: string) => (
          <SelectItem
            className="cursor-pointer hover:bg-secondary"
            key={lang}
            value={lang}
          >
            {new Intl.DisplayNames([lang], { type: "language" }).of(lang) || lang}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
