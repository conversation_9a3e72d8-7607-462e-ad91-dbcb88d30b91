.markdown {
  @apply text-lg leading-relaxed ;
}

.markdown p,
.markdown ul,
.markdown ol,
.markdown blockquote {
  @apply my-6;
}

.markdown h1 {
  @apply text-4xl mt-12 mb-4 leading-snug;
}

.markdown h2 {
  @apply text-3xl mt-12 mb-4 leading-snug;
}

.markdown h3 {
  @apply text-2xl mt-8 mb-4 leading-snug;
}

.markdown table {
  @apply w-full my-6 border-collapse border-2 border-border;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.markdown thead {
  @apply bg-muted;
}

.markdown tr {
  @apply border-b border-border;
}

.markdown tr:hover {
  @apply bg-muted/50;
}

.markdown th,
.markdown td {
  @apply border border-border p-3 text-left;
  min-width: 100px;
}

.markdown th {
  @apply bg-muted font-bold text-sm text-muted-foreground;
}

.markdown hr {
  @apply my-8 border-t border-border;
}

.markdown blockquote {
  @apply pl-4 border-l-4 border-border italic;
}

.markdown code {
  @apply bg-muted px-2 py-1 rounded;
}

.markdown details {
  @apply my-4;
}

.markdown summary {
  @apply cursor-pointer;
}

.markdown ul {
  @apply my-6 list-disc list-inside;
}

.markdown ul li {
  @apply pl-2 mb-2;
  position: relative;
}

.markdown ul li::marker {
  @apply text-muted-foreground;
  font-weight: bold;
}
