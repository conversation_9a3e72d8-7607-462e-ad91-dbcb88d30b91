import fs from "fs";
import matter from "gray-matter";
import { join } from "path";
import { Post } from "../interfaces/post";

// 博客文章的根目录路径
const postsDirectory = join(process.cwd(), "_posts");

// 获取指定语言目录下的所有文章文件名
export function getPostSlugs(locale: string) {
  const languageDir = join(postsDirectory, locale);
  try {
    return fs.readdirSync(languageDir);
  } catch (error) {
    return fs.readdirSync(join(postsDirectory, 'en')); // 降级到英文
  }
}

// 根据slug和语言获取单篇文章内容
export function getPostBySlug(slug: string, locale: string): Post | null {
  const realSlug = slug.replace(/\.md$/, "");
  const fullPath = join(postsDirectory, locale, `${realSlug}.md`);
  
  try {
    const fileContents = fs.readFileSync(fullPath, "utf8");
    const { data, content } = matter(fileContents);
    return { ...data, slug: realSlug, content } as Post;
  } catch (error) {
    if (locale !== 'en') {
      return getPostBySlug(slug, 'en');
    }
    return null;
  }
}

// 获取所有文章，支持按标签筛选和数量限制
export function getAllPosts(locale: string = 'en', tag?: string, limit: number = 50): Post[] {
  const slugs = getPostSlugs(locale);
  const posts = slugs
    .map((slug) => getPostBySlug(slug, locale))
    .filter((post) => post !== null)
    .filter((post) => !tag || post.tags?.includes(tag))
    .sort((post1, post2) => (post1.date > post2.date ? -1 : 1))
    .slice(0, limit);
  return posts;
}

