type CoverImageProps = {
    src: string;
    alt: string;
  }
  
  const CoverImage = ({ src, alt }: CoverImageProps) => {
    return (
      <div className="relative w-[158px] h-[158px]">
        <img
          src={src}
          alt={alt}
          className="absolute inset-0 w-full h-full object-cover rounded-lg"
          width={158}
          height={158}
        />
      </div>
    );
  }
  
  export default CoverImage;