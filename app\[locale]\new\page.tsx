import { getAllPosts } from "@/lib/api";
import { setRequestLocale } from "next-intl/server";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import Link from "next/link";

export default async function NewGamesPage({ params: { locale } }: { params: { locale: string } }) {
  setRequestLocale(locale);

  // 获取所有带 "game" 标签的文章，并按日期降序排列，取最新10个
  const allGames = getAllPosts(locale, "game");
  const newGames = allGames
    .sort((a, b) => (a.date > b.date ? -1 : 1))
    .slice(0, 10);

  return (
    <div className="max-w-7xl mx-auto py-8 px-4">
      <h1 className="text-4xl font-bold mb-8 text-center bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent">
        New Games
      </h1>
      
      <div className="grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
        {newGames.map((game) => (
          <Link
            key={game.slug}
            href={`/${locale}/game/${game.slug}`}
            className="group transition-all duration-300 hover:scale-105 hover:shadow-xl"
          >
            <div className="bg-card rounded-lg shadow-md overflow-hidden border border-border hover:border-primary/50 transition-all h-full flex flex-col">
              {/* 游戏封面图片 */}
              <div className="relative aspect-video w-full">
                <img
                  src={game.coverImage}
                  alt={game.gamename || game.title}
                  className="w-full h-[140px] object-cover group-hover:brightness-110 transition-all duration-300"
                />
                {/* 悬停时的播放按钮 */}
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300 flex items-center justify-center">
                  <div className="opacity-0 group-hover:opacity-100 transition-all duration-300">
                    <Button 
                      size="sm" 
                      className="bg-green-600 hover:bg-green-700 text-white rounded-full px-3 py-1"
                    >
                      <ArrowRight className="h-3 w-3 mr-1" />
                      Play
                    </Button>
                  </div>
                </div>
              </div>
              
              {/* 游戏信息 */}
              <div className="p-3 flex-1 flex flex-col">
                <h3 className="font-semibold text-sm text-card-foreground mb-1 group-hover:text-primary transition-colors overflow-hidden" style={{
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical'
                }}>
                  {game.gamename || game.title}
                </h3>
                <p className="text-xs text-muted-foreground mb-2 overflow-hidden" style={{
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical'
                }}>
                  {game.excerpt}
                </p>
                <p className="text-xs text-gray-400 mt-auto">
                  {new Date(game.date).toLocaleDateString()}
                </p>
              </div>
            </div>
          </Link>
        ))}
      </div>
      
      {/* 如果没有游戏，显示空状态 */}
      {newGames.length === 0 && (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🎮</div>
          <h3 className="text-xl font-semibold text-muted-foreground mb-2">
            No new games available
          </h3>
          <p className="text-muted-foreground">
            Check back later for exciting new games!
          </p>
        </div>
      )}
    </div>
  );
}