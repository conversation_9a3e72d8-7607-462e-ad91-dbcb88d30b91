#
多语言
messages
config/languages.ts
i18n/routing.ts
middleware.ts

# 部署
fly deploy
.dockerignore
dockerfile
fly.toml
.github/workflows/fly-deploy.yml

# 修改meta 
app/[locale]/game/[slug]/page.tsx

aahome 是主页
    metadataBase: new URL(process.env.NEXT_PUBLIC_URL || ""),
    ...(params.slug === 'aahome' ? {
      alternates: {
        canonical: '/',
      },
    } : {}),
    openGraph: {
      title,
      // images: [post.ogImage.url],
