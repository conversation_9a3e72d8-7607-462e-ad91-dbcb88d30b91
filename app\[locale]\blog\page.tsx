// download catalog

import <PERSON>Bottom from "@/components/GameBottom";
import { Link } from "@/i18n/routing";
import { getAllPosts, getPostBySlug } from "@/lib/api";
import { setRequestLocale } from 'next-intl/server';
import { supportedLanguages } from '@/config/languages';
import markdownToHtml from "@/lib/markdownToHtml";
import { notFound } from "next/navigation";
import { PostBody } from "@/components/blog/PostBody";
import { Button } from "@/components/ui/button";
import { ArrowRight, Download } from "lucide-react";

export default async function AllSprunkiGames({ params: { locale } }: { params: { locale: string } }) {
    setRequestLocale(locale);
    const allPosts = getAllPosts(locale, "blog");
    // 过滤出带有 download 标签的页面

    return (
        <div>
            <section className="mt-8 bg-gray-100/50 rounded-lg shadow p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {allPosts.map((post) => (
                        <Link 
                            key={post.slug} 
                            href={`/blog/${post.slug}`}
                            className="no-underline" 
                        >
                            <div className="bg-card p-4 rounded-lg shadow-sm hover:shadow-md transition-all">
                                <div className="flex items-center space-x-4">
                                    {/* <img
                                        src={post.coverImage}
                                        alt={post.gamename || ''}
                                        className="w-16 h-16 rounded-lg object-cover"
                                    /> */}
                                    <div className="flex-1">
                                        <h3 className="font-medium text-lg text-card-foreground">{post.title}</h3>
                                    </div>
                                    <Button variant="secondary" className="bg-green-600 hover:bg-green-700">
                                        <ArrowRight className="mr-2 h-4 w-4" />
                                        Read More
                                    </Button>
                                </div>
                            </div>
                        </Link>
                    ))}
                </div>

            </section>
        </div>
    )
}

export function generateStaticParams() {
    return supportedLanguages.map((locale) => ({ locale }));
}