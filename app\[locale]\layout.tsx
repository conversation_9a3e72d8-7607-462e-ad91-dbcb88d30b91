import "./globals.css";
import Script from "next/script";
import { NextIntlClientProvider } from "next-intl";
import { getMessages } from "next-intl/server";
import Header from "@/components/Header";
import { setRequestLocale } from 'next-intl/server';
import { notFound } from 'next/navigation';
import { supportedLanguages } from '@/config/languages';
import Footer from "@/components/Footer";
import { Inter as FontSans } from "next/font/google";

const fontSans = FontSans({
  subsets: ["latin"],
  variable: "--font-sans",
});

// 添加 generateStaticParams
export function generateStaticParams() {
  return supportedLanguages.map((locale) => ({ locale }));
}

export default async function RootLayout({
  children,
  params: { locale }, 
}: Readonly<{
  children: React.ReactNode;
  params: { locale: string };
}>) {
  // 使用 supportedLanguages 验证 locale
  if (!supportedLanguages.includes(locale)) {
    notFound();
  }

  // 启用静态渲染
  setRequestLocale(locale);

  const messages = await getMessages();

  return (
    <html lang={locale} suppressHydrationWarning>
      <head>
        {/* 点击统计 click.pageview.click */}
        <Script
          defer
          data-domain="howtodateanentity.org"
          src="https://click.pageview.click/js/script.js"
        ></Script>

        {/* 谷歌分析 */}
        <Script
          src="https://www.googletagmanager.com/gtag/js?id=G-3H8KZPE7KK"
          strategy="afterInteractive"
        />
        <Script id="google-analytics" strategy="afterInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-3H8KZPE7KK');
          `}
        </Script>

        {/* 谷歌广告 */}
        <Script
          async
          src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-9687738666635224"
          crossOrigin="anonymous"
          strategy="afterInteractive"
        />
      </head>

      <body
        className={` antialiased bg-background  font-sans ${fontSans.variable}`}
      >
        <NextIntlClientProvider messages={messages}>
        <Header />
        {children}
        </NextIntlClientProvider>
        <Footer />
      </body>
    </html>
  );
}
