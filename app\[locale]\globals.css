@tailwind base;
@tailwind components;
@tailwind utilities;

@import "theme.css";


.dot-grid-bg {
  background-image: radial-gradient(#e5e7eb 1px, transparent 2px);
  background-size: 16px 16px;
}

@font-face {
  font-family: 'PixelFont';
  src: url('/fonts/Silkscreen-Regular.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

html {
  scroll-behavior: smooth;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }

@layer base {
  
  :root  {
  --background: 208 100% 95%;
  --foreground: 208 5% 0%;
  --card: 208 50% 90%;
  --card-foreground: 208 5% 10%;
  --popover: 208 100% 95%;
  --popover-foreground: 208 100% 0%;
  --primary: 208 100% 48%;
  --primary-foreground: 0 0% 100%;
  --secondary: 208 30% 70%;
  --secondary-foreground: 0 0% 0%;
  --muted: 170 30% 85%;
  --muted-foreground: 208 5% 35%;
  --accent: 170 30% 80%;
  --accent-foreground: 208 5% 10%;
  --destructive: 0 100% 30%;
  --destructive-foreground: 208 5% 90%;
  --border: 208 30% 50%;
  --input: 208 30% 18%;
  --ring: 208 100% 48%;
  --radius: 0.5rem;
      }
  .dark  {
  --background: 208 50% 5%;
  --foreground: 208 5% 90%;
  --card: 208 50% 0%;
  --card-foreground: 208 5% 90%;
  --popover: 208 50% 5%;
  --popover-foreground: 208 5% 90%;
  --primary: 208 100% 48%;
  --primary-foreground: 0 0% 100%;
  --secondary: 208 30% 10%;
  --secondary-foreground: 0 0% 100%;
  --muted: 170 30% 15%;
  --muted-foreground: 208 5% 60%;
  --accent: 170 30% 15%;
  --accent-foreground: 208 5% 90%;
  --destructive: 0 100% 30%;
  --destructive-foreground: 208 5% 90%;
  --border: 208 30% 18%;
  --input: 208 30% 18%;
  --ring: 208 100% 48%;
  --radius: 0.5rem;
     }
}

}
