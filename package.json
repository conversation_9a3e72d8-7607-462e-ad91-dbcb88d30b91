{"name": "blog-18n", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "postbuild": "next-sitemap"}, "dependencies": {"@heroicons/react": "^2.2.0", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-slot": "^1.1.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "fs": "0.0.1-security", "gray-matter": "^4.0.3", "lucide-react": "^0.468.0", "next": "14.2.20", "next-intl": "^3.19.0", "next-sitemap": "^4.2.3", "react": "^18", "react-country-flag": "^3.1.0", "react-dom": "^18", "react-icons": "^5.4.0", "remark": "^15.0.1", "remark-gfm": "^4.0.1", "remark-html": "^16.0.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@flydotio/dockerfile": "^0.5.9", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.20", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5", "vercel": "39.1.1"}, "overrides": {"vercel": "$vercel"}}