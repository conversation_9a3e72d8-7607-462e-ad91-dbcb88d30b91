import { PostBody } from "@/components/blog/PostBody";
import PageTitle from "@/components/blog/PageTitle";
import { getAllPosts, getPostBySlug } from "@/lib/api";
import markdownToHtml from "@/lib/markdownToHtml";
import { Metadata } from "next";
import { notFound } from "next/navigation";
import Footer from "@/components/Footer";
import MoreGames from "@/components/MoreGames";
import GameBottom from "@/components/GameBottom";
import { GameContainer } from "@/components/GameContainer";
import GameSidebar from "@/components/GameSidebar";
import { setRequestLocale } from "next-intl/server";
import GameSideBarHot from "@/components/GameSideBarHot";
import GameBottomNew from "@/components/GameBottomNew";
import Hero from "@/components/Hero";

export default async function Home({
  params: { locale },
}: {
  params: { locale: string };
}) {
  setRequestLocale(locale);
  const post = getPostBySlug("aahome", locale);
  const taggedPosts = getAllPosts(locale, "game");

  if (!post) {
    notFound();
  }

  const content = await markdownToHtml(post.content || "");
  const firstHalfPosts =
    taggedPosts.length <= 1
      ? taggedPosts
      : taggedPosts.slice(0, Math.ceil(taggedPosts.length / 2));
  const secondHalfPosts =
    taggedPosts.length <= 1
      ? taggedPosts
      : taggedPosts.slice(Math.ceil(taggedPosts.length / 2));



  return (
    <main className="container mx-auto">
              <Hero />

      <div className="flex flex-col lg:flex-row">
        <article className="flex-1">
          <div className="flex flex-col items-start px-4 lg:px-20">
            {post.iframeUrl && <GameContainer iframeUrl={post.iframeUrl} gamename={post.gamename || ''} coverImage={post.coverImage || ''} />}

            <div className="flex items-center px-4 lg:px-20">
              <p className="text-sm text-muted-foreground">
                Press F5 to refresh if game fails to load
              </p>
            </div>

            <div className="w-full">
              <div className="lg:hidden">
                {/* <GameSideBarHot params={{ locale }} /> */}
              </div>
              {/* <GameBottomNew params={{ locale }} /> */}
              <PostBody content={content} />
            </div>
          </div>
        </article>
        {/* <div className="hidden lg:block">
        <GameSideBarHot params={{ locale }} />
        </div> */}
      </div>
    </main>
  );
}

export async function generateMetadata({
  params: { locale },
}: {
  params: { locale: string };
}): Promise<Metadata> {
  const post = getPostBySlug("aahome", locale);

  if (!post) {
    return notFound();
  }

  const title = `${post.title}`;
  const description = post.excerpt;
  return {
    title,
    description,
    // openGraph: {
    //   title,
    //   images: [post.ogImage.url],
    // },
  };
}
