// version

import { getAllPosts, getPostBySlug } from "@/lib/api";
import { setRequestLocale } from 'next-intl/server';
import { supportedLanguages } from '@/config/languages';
import markdownToHtml from "@/lib/markdownToHtml";
import { notFound } from "next/navigation";
import { PostBody } from "@/components/blog/PostBody";

export default async function BlogPost({ params: { locale, slug } }: { params: { locale: string, slug: string } }) {
    setRequestLocale(locale);
    const post = getPostBySlug(slug, locale);
    
    if (!post || !post.tags?.includes('blog')) {
        notFound();
    }

    const content = await markdownToHtml(post.content || "");

    return (
        <div>
            <section className="mt-8 bg-gray-100/50 rounded-lg shadow p-6">
                <PostBody content={content} />
            </section>
        </div>
    )
}

export function generateStaticParams() {
    const params = [];
    
    for (const locale of supportedLanguages) {
        try {
            const posts = getAllPosts(locale, 'blog');
            for (const post of posts) {
                params.push({
                    locale,
                    slug: post.slug
                });
            }
        } catch (error) {
            console.warn(`No blog posts found for locale: ${locale}`);
        }
    }
    
    return params;
}
  