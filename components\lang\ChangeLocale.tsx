"use client";

import { Check, ChevronDown } from "lucide-react";
import { useLocale } from "next-intl";
import { useRouter, usePathname, useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { supportedLanguages } from "../../config/languages";

export default function ChangeLocale() {
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();
  const params = useParams();
  const [isOpen, setIsOpen] = useState(false);

  const languages = supportedLanguages.map((code) => ({
    code,
    label: new Intl.DisplayNames([code], { type: "language" }).of(code) || code,
  }));

  const handleSelect = (newLocale: string) => {
    const currentPath = pathname.replace(`/${locale}`, '');
    router.push(`/${newLocale}${currentPath}`);
    setIsOpen(false);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isOpen && !(event.target as Element).closest(".language-selector")) {
        setIsOpen(false);
      }
    };

    document.addEventListener("click", handleClickOutside);

    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, [isOpen]);

  return (
    <div className="relative font-semibold language-selector">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center justify-between w-full px-4 py-2 text-sm font-semibold text-left bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 "
      >
        <div className="flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 16 16"
            fill="currentColor"
            className="h-5 w-5 mr-2"
          >
            <path
              fillRule="evenodd"
              d="M11 5a.75.75 0 0 1 .688.452l3.25 7.5a.75.75 0 1 1-1.376.596L12.89 12H9.109l-.67 1.548a.75.75 0 1 1-1.377-.596l3.25-7.5A.75.75 0 0 1 11 5Zm-1.24 5.5h2.48L11 7.636 9.76 10.5ZM5 1a.75.75 0 0 1 .75.75v1.261a25.27 25.27 0 0 1 2.598.211.75.75 0 1 1-.2 1.487c-.22-.03-.44-.056-.662-.08A12.939 12.939 0 0 1 5.92 8.058c.237.304.488.595.752.873a.75.75 0 0 1-1.086 1.035A13.075 13.075 0 0 1 5 9.307a13.068 13.068 0 0 1-2.841 2.546.75.75 0 0 1-.827-1.252A11.566 11.566 0 0 0 4.08 8.057a12.991 12.991 0 0 1-.554-.938.75.75 0 1 1 1.323-.707c.**************.15.271.388-.68.708-1.405.952-2.164a23.941 23.941 0 0 0-*********.75 0 0 1-.2-1.487c.853-.114 1.72-.185 2.598-.211V1.75A.75.75 0 0 1 5 1Z"
              clipRule="evenodd"
            />
          </svg>
          {languages.find((lang) => lang.code === locale)?.label}
        </div>
        <ChevronDown className="w-4 h-4 ml-2" aria-hidden="true" />
      </button>

      {isOpen && (
        <ul className="absolute z-10 w-full py-1 mt-1 overflow-auto text-sm bg-white rounded-md shadow-lg max-h-60 ring-1 ring-black ring-opacity-5 focus:outline-none">
          {languages.map((lang) => (
            <li
              key={lang.code}
              className={`cursor-pointer select-none relative py-2 px-3 flex items-center justify-between ${
                locale === lang.code
                  ? "text-indigo-600 "
                  : "text-gray-900 hover:bg-gray-100"
              }`}
              onClick={() => handleSelect(lang.code)}
            >
              {lang.label}
              {locale === lang.code && <Check className="w-4 h-4" />}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}
