import { Post } from "@/interfaces/post";

export default function MoreGames({allPosts}: {allPosts: Post[]}) {
    return (
        <div>
                {allPosts.map((post) => (
                    <a key={post.slug} href={`/${post.slug}`} className="mb-2 ">
                        <div className="  ">
                            <img src={post.coverImage} />
                        </div>
                        <div className="text-sm mb-3 -mt-6 text-white bg-yellow-500">
                                {post.gamename}                          
                        </div>
                    </a>

                ))}
        </div>
    )
}