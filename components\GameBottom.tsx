import { Post } from "@/interfaces/post";
import Image from "next/image";
import { Link } from "@/i18n/routing";

export default function GameBottom({ posts }: { posts: Post[] }) {
    return (
        <section className="bg-secondary/50 rounded-lg shadow">
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2 p-2">
                {posts.map((post) => (
                    <Link 
                        key={post.slug} 
                        href={`/game/${post.slug}`}
                        className="group transition-all hover:opacity-90" 
                    >
                        <div className="flex flex-col items-center">
                            <img
                                src={post.coverImage}
                                alt={post.gamename || ''}
                                className="w-full h-[120px] sm:h-[140px] lg:h-[158px] rounded-t-lg object-cover"
                            />
                            <div className="text-sm py-1 px-2 text-primary-foreground bg-primary rounded-b-lg w-full">
                                {post.gamename}
                            </div>
                        </div>
                    </Link>
                ))}
            </div>
        </section>
    );
} 