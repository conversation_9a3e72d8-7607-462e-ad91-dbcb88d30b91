"use client";

import { <PERSON> } from "@/i18n/routing";
import { FC, useState } from "react";
import { FaMusic } from "react-icons/fa";
import ChangeLocale from "./lang/ChangeLocale";
import { useTranslations } from "next-intl";
import MeloLocale from "./nextintl/MeloLocale";
import { Righteous } from 'next/font/google'

const righteous = Righteous({ 
  weight: '400',
  subsets: ['latin'],
  variable: '--font-righteous',
})

const Header: FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const t = useTranslations('header');

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  return (
    <header className="shadow-sm w-full bg-white/10 backdrop-blur-sm rounded-lg mt-1 mb-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo and Brand Name */}
          <div className="flex items-center text-foreground">
            <Link href="/" className="flex items-center  lg:text-3xl tracking-wider">
              <img src="/logo.png" alt="logo" className="w-10 h-10 mr-2" />
              <span className={`text-sm ${righteous.className}`}>
                {process.env.NEXT_PUBLIC_BRAND_NAME}
              </span>
              {/* commit 2*/}
            </Link>
          </div>

          {/* Navigation Links */}
          <nav className="hidden md:flex space-x-2 lg:space-x-8 text-foreground items-center text-sm lg:text-base">
            {/* <Link
              href="/all-sprunki-games"
              className="hover:bg-primary/80 bg-primary text-primary-foreground px-4   rounded-full transition-colors duration-200 font-medium"
            >
              {t('allgames')}
            </Link>
            <Link 
              href="/sprunki-phase-1-15"
              className="hover:bg-primary/80 bg-primary text-primary-foreground px-4 text  rounded-full transition-colors duration-200 font-medium"
            >
              {t('sprunki phase 1-15')}
            </Link>
            <Link 
              href="/hot"
              className="hover:bg-primary/80 bg-primary text-primary-foreground px-4 text  rounded-full transition-colors duration-200 font-medium"
            >
              {t('hot games')}
            </Link>
            <Link 
              href="/new"
              className="hover:bg-primary/80 bg-primary text-primary-foreground px-4 text  rounded-full transition-colors duration-200 font-medium"
            >
              {t('new games')}
            </Link> */}
            <Link 
              href="/new"
              className="bg-gradient-to-r from-purple-500 to-indigo-500 text-white font-bold rounded-full px-6 py-2 shadow-lg border-2 border-white hover:scale-105 transition-all text-base"
            >
              {t('new games')}
            </Link>
             <Link 
            href="/blog"
            className="bg-gradient-to-r from-purple-500 to-indigo-500 text-white font-bold rounded-full px-6 py-2 shadow-lg border-2 border-white hover:scale-105 transition-all text-base"
          >
            {t('blog')}
          </Link>
            <div className="text-foreground ">
            <MeloLocale />
            </div>

          </nav>

          {/* Blog Link for Desktop */}


          {/* Mobile Menu Button */}
          <div className="md:hidden ">
            <button
              type="button"
              onClick={toggleMenu}
              className="inline-flex items-center bg-background justify-center p-2 rounded-md  hover:bg-muted focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary"
              aria-expanded={isOpen}
            >
              <span className="sr-only">Open main menu</span>
              {/* Menu Icon */}
              <svg
                className="block h-6 w-6"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Menu Panel */}
      <div className={`md:hidden ${isOpen ? "block" : "hidden"}`}>
        <div className="px-2 pt-2 flex flex-col pb-3 space-y-1 text-foreground">
          {/* <Link
            href="/all-sprunki-games"
            className="hover:bg-primary/80 bg-primary text-primary-foreground px-4 py-2 rounded-full transition-colors duration-200 font-medium"
          >
            {t('allgames')}
          </Link>
          <Link 
            href="/sprunki-phase-1-15"
            className="hover:bg-primary/80 bg-primary text-primary-foreground px-4 py-2 rounded-full transition-colors duration-200 font-medium"
          >
            {t('sprunki phase 1-15')}
          </Link>
          <Link 
            href="/hot"
            className="hover:bg-primary/80 bg-primary text-primary-foreground px-4 py-2 rounded-full transition-colors duration-200 font-medium"
          >
            {t('hot games')}
          </Link>
          <Link 
            href="/new"
            className="hover:bg-primary/80 bg-primary text-primary-foreground px-4 py-2 rounded-full transition-colors duration-200 font-medium"
          >
            {t('new games')}
          </Link> */}
          {/* Blog Link for Mobile */}

          <div className="text-foreground mx-auto">
            <MeloLocale />
          </div>


        </div>
      </div>
    </header>
  );
};

export default Header;
