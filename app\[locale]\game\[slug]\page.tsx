import { getPostBySlug } from "@/lib/api";
import { setRequestLocale } from "next-intl/server";
import markdownToHtml from "@/lib/markdownToHtml";
import { notFound } from "next/navigation";
import { PostBody } from "@/components/blog/PostBody";

export default async function GameDetailPage({ params: { locale, slug } }: { params: { locale: string, slug: string } }) {
  setRequestLocale(locale);
  const post = getPostBySlug(slug, locale);

  if (!post || !post.tags?.includes("game")) {
    notFound();
  }

  const content = await markdownToHtml(post.content || "");

  return (
    <div>
      <h1 className="text-3xl font-bold mb-4">{post.gamename || post.title}</h1>
      {post.iframeUrl && (
        <div className="w-full flex justify-center my-6">
          <iframe
            src={post.iframeUrl}
            title={post.gamename || post.title}
            width="100%"
            height="600"
            allowFullScreen
            frameBorder="0"
            className="rounded-lg shadow-2xl bg-black max-w-4xl w-full h-[600px] mx-auto"
          />
        </div>
      )}
      <PostBody content={content} />
    </div>
  );
} 