import { Link } from "@/i18n/routing";
import { setRequestLocale } from "next-intl/server";
import { supportedLanguages } from "@/config/languages";
import { getPostBySlug } from "@/lib/api";
import { PostBody } from "@/components/blog/PostBody";
import markdownToHtml from "@/lib/markdownToHtml";
import { notFound } from "next/navigation";

export async function generateStaticParams() {
  return supportedLanguages.map((locale) => ({ locale }));
}

export default async function Tos({
    
  params: { locale },
}: {
  params: { locale: string };
}) {
  setRequestLocale(locale);
  const Post = getPostBySlug("tos", locale);
  if (!Post || !Post.content) {
    notFound();
  }
  const content = await markdownToHtml(Post.content || "");
  return (
    <div>
      <div>
        <PostBody content={content} />
      </div>
    </div>
  );
}
