import { getAllPosts } from "@/lib/api";
import GameBottom from "./GameBottom";
import { FaStar } from "react-icons/fa";
import { useTranslations } from "next-intl";

export default function GameBottomNew({ params: { locale } }: { params: { locale: string } }) {
    const taggedPosts = getAllPosts(locale, "game");
    const  t  = useTranslations("gamerecommend");
    const newGames = taggedPosts
    .sort((a, b) => {
        return new Date(b.date).getTime() - new Date(a.date).getTime()
    })
    .slice(0, 40);

    return (
        <div>
            <div className="text-2xl font-bold flex items-center text-yellow-500 mt-8">
                <FaStar className="w-6 h-6 mr-2 " />
                {t("newgames")}
            </div>
            <GameBottom posts={newGames} />
        </div>
    )

    
}