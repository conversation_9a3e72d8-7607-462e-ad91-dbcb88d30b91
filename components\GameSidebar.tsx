import { Post } from "@/interfaces/post";
import Image from "next/image";
import { Link } from "@/i18n/routing";

export default function GameSidebar({ posts }: { posts: Post[] }) {
    return (
        <div className="w-full lg:w-80  lg:mr-16 mt-8 lg:mt-0">
            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-2 gap-2 bg-secondary/50 rounded-lg shadow">
                {posts.map((post) => (
                    <Link 
                        key={post.slug} 
                        href={`/game/${post.slug}`}
                        className="group transition-all hover:opacity-90 inline-block" 
                    >
                        <div className="relative aspect-video w-full">
                            <img
                                src={post.coverImage}
                                alt={post.gamename || ''}
                                className="object-cover w-full h-[120px] lg:h-[158px] rounded-t-lg"
                            />
                        </div>
                        <div className="text-sm py-1 px-2 bg-primary text-primary-foreground rounded-b-lg">
                            {post.gamename}
                        </div>
                    </Link>
                ))}
            </div>
        </div>
    );
} 