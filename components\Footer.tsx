import { supportedLanguages } from "@/config/languages";
import { Link } from "@/i18n/routing";

const Footer = () => {
  return (
    <footer className="bg-card rounded-lg shadow p-8 mt-16">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* 网站介绍 */}
          <div className="website-intro">
            <h3 className="font-bold text-lg mb-3 text-foreground">
              {process.env.NEXT_PUBLIC_BRAND_NAME}
            </h3>
            <p className="text-muted-foreground">
            A Bizarre Dating Sim x Analog Horror Visual Novel

            </p>
          </div>

          {/* 语言链接 */}
          <div className="language-links">
            <h3 className="font-bold text-lg mb-3 text-foreground">Languages</h3>
            <ul className="space-y-2">
              {supportedLanguages.map((code) => (
                <li key={code}>
                  <a 
                    href={`/${code}`} 
                    className="text-muted-foreground hover:text-foreground transition-colors"
                  >
                    {new Intl.DisplayNames([code], { type: "language" }).of(code)}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* 法律信息 */}
          <div className="legal-info">
            <h3 className="font-bold text-lg mb-3 text-foreground">Legal</h3>
            <ul className="space-y-2">
              <li><Link href="/privacy" className="text-muted-foreground hover:text-foreground transition-colors">Privacy Policy</Link></li>
              <li><Link href="/tos" className="text-muted-foreground hover:text-foreground transition-colors">Terms of Service</Link></li>
              <li><p className="text-muted-foreground">© 2024 {process.env.NEXT_PUBLIC_BRAND_NAME}. All rights reserved</p></li>
            </ul>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
