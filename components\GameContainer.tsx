'use client';

import { useEffect, useRef, useState } from 'react';
import { 
    enterFullscreen, 
    exitFullscreen, 
    handleFullscreenChange, 
    autoEnterFullscreen 
} from '@/utils/fullscreen';
import { FaExpand, FaCompress } from 'react-icons/fa';
import { useTranslations } from 'next-intl';

interface GameContainerProps {
    iframeUrl: string;
    gamename: string;
    coverImage: string;
}

export function GameContainer({ iframeUrl, gamename, coverImage }: GameContainerProps) {
    const [showGame, setShowGame] = useState(false);
    const containerRef = useRef<HTMLDivElement>(null);
    const iframeRef = useRef<HTMLIFrameElement>(null);
    const fullscreenBtnRef = useRef<HTMLButtonElement>(null);
    const exitFullscreenBtnRef = useRef<HTMLButtonElement>(null);
    const watermarkRef = useRef<HTMLDivElement>(null);
    const  t = useTranslations('gamecontainer');
    
    useEffect(() => {
        if (!showGame) return;
        
        const container = containerRef.current;
        const iframe = iframeRef.current;
        const fullscreenBtn = fullscreenBtnRef.current;
        const exitFullscreenBtn = exitFullscreenBtnRef.current;
        const watermark = watermarkRef.current;

        if (!container || !iframe || !fullscreenBtn || !exitFullscreenBtn || !watermark) return;

        const handleFullscreenBtn = () => {
            enterFullscreen(container, iframe, fullscreenBtn, exitFullscreenBtn, watermark);
        };

        const handleExitFullscreenBtn = () => {
            exitFullscreen(container, iframe, fullscreenBtn, exitFullscreenBtn, watermark);
        };

        const handleFullscreenChangeEvent = () => {
            handleFullscreenChange(container, iframe, fullscreenBtn, exitFullscreenBtn, watermark);
        };

        // Add event listeners
        fullscreenBtn.addEventListener('click', handleFullscreenBtn);
        exitFullscreenBtn.addEventListener('click', handleExitFullscreenBtn);
        document.addEventListener('fullscreenchange', handleFullscreenChangeEvent);
        document.addEventListener('webkitfullscreenchange', handleFullscreenChangeEvent);
        document.addEventListener('mozfullscreenchange', handleFullscreenChangeEvent);
        document.addEventListener('MSFullscreenChange', handleFullscreenChangeEvent);

        // Auto enter fullscreen if needed
        autoEnterFullscreen(container, iframe, fullscreenBtn, exitFullscreenBtn, watermark);

        // Cleanup
        return () => {
            fullscreenBtn.removeEventListener('click', handleFullscreenBtn);
            exitFullscreenBtn.removeEventListener('click', handleExitFullscreenBtn);
            document.removeEventListener('fullscreenchange', handleFullscreenChangeEvent);
            document.removeEventListener('webkitfullscreenchange', handleFullscreenChangeEvent);
            document.removeEventListener('mozfullscreenchange', handleFullscreenChangeEvent);
            document.removeEventListener('MSFullscreenChange', handleFullscreenChangeEvent);
        };
    }, [showGame]);

    return (
        <div
            ref={containerRef}
            id="game-container"
            className="game-container relative w-full max-w-4xl mx-auto"
        >
            {!showGame ? (
                <div className="relative w-full h-[600px]  overflow-hidden flex ">
                    <div className="absolute inset-0 flex flex-col items-center justify-center bg-gray-400 ">
                    <img
                        src={coverImage}
                        alt={`${gamename} Cover`}
                        className="w-[158px] h-[158px] object-cover mb-4"
                    />

                        <p className="text-4xl font-bold text-white mb-4">{gamename}</p>
                        <button
                        
                            onClick={() => setShowGame(true)}
                            className="bg-yellow-400 hover:bg-yellow-500 text-black font-bold py-3 px-8 rounded-full text-xl transition-all"
                        >
                            {t('playnow')}
                        </button>
                    </div>
                </div>
            ) : (
                <>
                    <iframe
                        ref={iframeRef}
                        id="game-iframe"
                        src={iframeUrl}
                        frameBorder="0"
                        className="w-full h-[600px]  shadow-2xl"
                    ></iframe>
                    <button
                        ref={fullscreenBtnRef}
                        id="fullscreen-btn"
                        className="absolute bottom-4 right-4 bg-blue-400 bg-opacity-60 text-white border-none py-2 px-4 rounded cursor-pointer text-sm transition duration-300 hover:bg-opacity-80 flex items-center gap-2"
                    >
                        <FaExpand /> Fullscreen
                    </button>
                    <button
                        ref={exitFullscreenBtnRef}
                        id="exit-fullscreen-btn"
                        className="absolute bottom-4 right-4 bg-blue-400 bg-opacity-60 text-white border-none py-2 px-4 rounded cursor-pointer text-sm transition duration-300 hover:bg-opacity-80 flex items-center gap-2 hidden"
                    >
                        <FaCompress /> Exit Fullscreen
                    </button>
                    <div
                        ref={watermarkRef}
                        id="domain-watermark"
                        className="absolute top-4 left-4 text-white text-xl font-bold opacity-50 hidden"
                    >
                        sprunki-incredibox.com
                    </div>
                </>
            )}
        </div>
    );
}
