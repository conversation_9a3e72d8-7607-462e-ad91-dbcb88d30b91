
export function isMobile(): boolean {
    if (typeof navigator === 'undefined') return false;
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

export function isLandscape(): boolean {
    if (typeof window === 'undefined') return false;
    return window.innerWidth > window.innerHeight;
}

export function handleMobileFullscreen(
    gameContainer: HTMLElement,
    gameIframe: HTMLIFrameElement,
    fullscreenBtn: HTMLElement,
    exitFullscreenBtn: HTMLElement,
    domainWatermark: HTMLElement
): void {
    setTimeout(() => {
        domainWatermark.style.display = 'block';
    }, 1000);

    const header = document.querySelector('header');
    if (header) header.style.display = 'none';
    
    gameContainer.style.height = '100vh';
    gameContainer.style.width = '100vw';
    gameContainer.style.position = 'fixed';
    gameContainer.style.top = '0';
    gameContainer.style.left = '0';
    gameContainer.style.zIndex = '9999';
    gameContainer.style.backgroundColor = '#000';

    if (isLandscape()) {
        gameIframe.style.width = '100vw';
        gameIframe.style.height = '100vh';
        gameIframe.style.transform = 'none';
        gameIframe.style.position = 'static';
        exitFullscreenBtn.style.transform = 'none';
        domainWatermark.style.display = 'block';
    } else {
        gameIframe.style.width = '100vh';
        gameIframe.style.height = '100vw';
        gameIframe.style.transform = 'rotate(90deg)';
        gameIframe.style.transformOrigin = 'top left';
        gameIframe.style.position = 'absolute';
        gameIframe.style.top = '0';
        gameIframe.style.left = '100%';
        exitFullscreenBtn.style.transform = 'rotate(90deg)';
        domainWatermark.style.display = 'none';
    }

    fullscreenBtn.style.display = 'none';
    exitFullscreenBtn.style.display = 'block';

    if ('orientation' in screen && typeof (screen.orientation as any).lock === 'function') {
        (screen.orientation as any).lock('landscape').catch(() => {
            console.log('Landscape lock not supported');
        });
    }
}

export function handleDesktopFullscreen(
    gameIframe: HTMLIFrameElement,
    fullscreenBtn: HTMLElement,
    exitFullscreenBtn: HTMLElement
): void {
    if (gameIframe.requestFullscreen) {
        gameIframe.requestFullscreen();
    } else if ((gameIframe as any).mozRequestFullScreen) {
        (gameIframe as any).mozRequestFullScreen();
    } else if ((gameIframe as any).webkitRequestFullscreen) {
        (gameIframe as any).webkitRequestFullscreen();
    } else if ((gameIframe as any).msRequestFullscreen) {
        (gameIframe as any).msRequestFullscreen();
    }
    
    fullscreenBtn.style.display = 'none';
    exitFullscreenBtn.style.display = 'block';
}

export function enterFullscreen(
    gameContainer: HTMLElement,
    gameIframe: HTMLIFrameElement,
    fullscreenBtn: HTMLElement,
    exitFullscreenBtn: HTMLElement,
    domainWatermark: HTMLElement
): void {
    if (isMobile()) {
        handleMobileFullscreen(gameContainer, gameIframe, fullscreenBtn, exitFullscreenBtn, domainWatermark);
    } else {
        handleDesktopFullscreen(gameIframe, fullscreenBtn, exitFullscreenBtn);
    }
}

export function exitFullscreen(
    gameContainer: HTMLElement,
    gameIframe: HTMLIFrameElement,
    fullscreenBtn: HTMLElement,
    exitFullscreenBtn: HTMLElement,
    domainWatermark: HTMLElement
): void {
    if (document.fullscreenElement) {
        document.exitFullscreen().catch((err) => {
            console.log('Error exiting fullscreen:', err);
        });
    } else if ((document as any).webkitFullscreenElement && (document as any).webkitExitFullscreen) {
        (document as any).webkitExitFullscreen();
    } else if ((document as any).mozFullScreenElement && (document as any).mozCancelFullScreen) {
        (document as any).mozCancelFullScreen();
    } else if ((document as any).msFullscreenElement && (document as any).msExitFullscreen) {
        (document as any).msExitFullscreen();
    }

    if (isMobile()) {
        const header = document.querySelector('header');
        if (header) header.style.display = 'block';
        gameContainer.removeAttribute('style');
        gameIframe.removeAttribute('style');
        domainWatermark.style.display = 'none';
        if (screen.orientation && screen.orientation.unlock) {
            screen.orientation.unlock();
        }
    }

    fullscreenBtn.style.display = 'block';
    exitFullscreenBtn.style.display = 'none';
}

export function handleFullscreenChange(
    gameContainer: HTMLElement,
    gameIframe: HTMLIFrameElement,
    fullscreenBtn: HTMLElement,
    exitFullscreenBtn: HTMLElement,
    domainWatermark: HTMLElement
): void {
    if (!document.fullscreenElement && 
        !(document as any).webkitFullscreenElement && 
        !(document as any).mozFullScreenElement && 
        !(document as any).msFullscreenElement) {
        exitFullscreen(gameContainer, gameIframe, fullscreenBtn, exitFullscreenBtn, domainWatermark);
    }
}

export function autoEnterFullscreen(
    gameContainer: HTMLElement,
    gameIframe: HTMLIFrameElement,
    fullscreenBtn: HTMLElement,
    exitFullscreenBtn: HTMLElement,
    domainWatermark: HTMLElement
): void {
    if (isMobile() && !isLandscape()) {
        handleMobileFullscreen(gameContainer, gameIframe, fullscreenBtn, exitFullscreenBtn, domainWatermark);
    }
}
