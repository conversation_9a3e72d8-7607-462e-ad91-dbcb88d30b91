import { createNavigation } from "next-intl/navigation";
import { defineRouting } from "next-intl/routing";
import { defaultLanguage, supportedLanguages } from "../config/languages";

export const routing = defineRouting({
  locales: supportedLanguages,
  defaultLocale: defaultLanguage,
  localePrefix: "as-needed",
  // @ts-ignore
  localeDetection: false,
});

export type Locale = (typeof routing.locales)[number];

// Lightweight wrappers around Next.js' navigation APIs
// that will consider the routing configuration
export const { Link, redirect, usePathname, useRouter } =
createNavigation(routing);
