import { Post } from "@/interfaces/post";
import GameSidebar from "./GameSidebar";
import { getAllPosts } from "@/lib/api";
import { FaFire } from 'react-icons/fa';
import { useTranslations } from "next-intl";

export default function GameSideBarHot({ params: { locale } }: { params: { locale: string } }) {
  const t = useTranslations("gamerecommend");
  const taggedPosts = getAllPosts(locale, "game");

  const hotgamesSlugs = [
    "retake-sprunki",
    "sprunki-relish",
    "sprunked",
    "sprunki-sinner",
    "incredibox-mustard",
    "sprunki-oc",
    "sprunki-retake-deluxe",
    "sprunki-phase-777",
    "corruptbox-2-but-sprunki",
    "sprunki-phase-4-definitive",
    "sprunkr",
    "sprunki-phase-15",
    "sprunki-wenda",
    "sprunki-phase-3-definitive",
    "sprunki-phase-4",
    "sprunki-sonic",
    "parodybox",
    "sprunki-phase-10",
    "sprunki-retake-final-update",
    "sprunki-infected-war",
    "sprunki-phase-11",
    "sprunker",
    "sprunk-happy-friends",
    "sprunki-phase-13",
    "pyramixed",
    "sprunki-phase-12",
    "corruptbox-3",
    "sprunki-phase-14",
    "sprunki-phase-1",
    "countryballs-sprunki-retake",
  ];

  const hotgamesPosts = taggedPosts.filter((post) =>
    hotgamesSlugs.includes(post.slug)
  );

  return (
    <div>
      <div className="text-2xl font-bold mt-4 flex items-center text-destructive">
        <FaFire className="w-6 h-6 mr-2" />
        {t("hotgames")}
      </div>
      <GameSidebar posts={hotgamesPosts} />
    </div>
  );
}
